import React, { useEffect } from 'react';
import styled from 'styled-components/macro';
import { StepWizardChildProps } from 'react-step-wizard';

import { WfInfoResponse, GroupForInfo, Group, Definition, Task } from 'models/response';

import utils from 'utils';

import { <PERSON><PERSON>ontainer, StyledColWrapper } from './styles';

import Modal from 'components/Modal';
import Dropdown from 'components/Input/Dropdown';
import TextInput from 'components/Input/TextInput';

const InputContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 15px;
`;
const InputLabel = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};
  margin-right: 15px;
  width: 120px;
  text-align: right;
`;

export interface Props extends StepWizardChildProps {
  infoForSuspend: WfInfoResponse;
  dropOptOne: { value: string; label: string }[];

  selectedReason: { value: string; label: string };
  setSelectedReason: (option: { value: string; label: string }) => void;

  selectedDescription: { value: number; label: string } | null;
  setSelectedDescription: (option: { value: number; label: string } | null) => void;

  infoInput: string;
  setInfoInput: (value: string) => void;

  setSelectedTask: (isActive?: boolean, task?: Task) => void;
  setSelectedGroups: (groups: Group[]) => void;
  setSelectedDefinition: (definition: Definition | null) => void;
  setSelectedWFDefinition: (definition: GroupForInfo | null) => void;

  preSelectedDescription: boolean;
  noInfo: boolean;
}

const InitialStep: React.FC<Partial<Props>> = (props) => {
  const {
    goToStep,
    infoForSuspend,
    dropOptOne,
    selectedReason,
    setSelectedReason,
    selectedDescription,
    setSelectedDescription,
    infoInput,
    setInfoInput,
    setSelectedTask,
    setSelectedGroups,
    setSelectedDefinition,
    setSelectedWFDefinition,
    preSelectedDescription,
    noInfo,
  } = props;
  const t = utils.intl.useTranslator();

  useEffect(() => {
    preSelectedDescription && goToStep && goToStep(3);
  }, [preSelectedDescription, goToStep]);

  return dropOptOne ? (
    <StepContainer>
      <Modal.Header title={t('send-to-workflow')} subtitle={t('reason-send-to-wf')} />
      <StyledColWrapper>
        <InputContainer>
          <InputLabel>{t('reason')}</InputLabel>
          <Dropdown
            disabled={preSelectedDescription || noInfo}
            options={dropOptOne}
            value={selectedReason}
            onChange={(option) => {
              setSelectedReason && setSelectedReason(option);
              setSelectedDescription && setSelectedDescription(null);
              setInfoInput && setInfoInput('');
              setSelectedDefinition && setSelectedDefinition(null);
              setSelectedWFDefinition && setSelectedWFDefinition(null);
              setSelectedTask && setSelectedTask();
              setSelectedGroups && setSelectedGroups([]);
            }}
          />
        </InputContainer>
        {selectedReason?.value === 'causale' ? (
          infoForSuspend ? (
            <InputContainer>
              <InputLabel>{t('causal')}</InputLabel>
              <Dropdown
                disabled={preSelectedDescription}
                options={utils.input.buildOptions(infoForSuspend.causals || [], 'causalName', 'idWfCausal')}
                value={selectedDescription}
                onChange={(option) => {
                  setSelectedDescription && setSelectedDescription(option);
                  setSelectedDefinition && setSelectedDefinition(null);
                  setSelectedTask && setSelectedTask();
                  setSelectedGroups && setSelectedGroups([]);
                }}
              />
            </InputContainer>
          ) : null
        ) : (
          <InputContainer>
            <InputLabel>{t('info')}</InputLabel>
            <TextInput
              value={infoInput}
              onChange={(e: React.FocusEvent<HTMLInputElement>) => {
                setInfoInput && setInfoInput(e.target.value);
              }}
            />
          </InputContainer>
        )}
      </StyledColWrapper>
    </StepContainer>
  ) : null;
};

export default InitialStep;
